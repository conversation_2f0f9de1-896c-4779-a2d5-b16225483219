PODS:
  - cloud_firestore (5.6.9):
    - Firebase/Firestore (= 11.13.0)
    - firebase_core
    - Flutter
  - Firebase/Analytics (11.13.0):
    - Firebase/Core
  - Firebase/Auth (11.13.0):
    - Firebase/CoreOnly
    - FirebaseAuth (~> 11.13.0)
  - Firebase/Core (11.13.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 11.13.0)
  - Firebase/CoreOnly (11.13.0):
    - FirebaseCore (~> 11.13.0)
  - Firebase/Database (11.13.0):
    - Firebase/CoreOnly
    - FirebaseDatabase (~> 11.13.0)
  - Firebase/Firestore (11.13.0):
    - Firebase/CoreOnly
    - FirebaseFirestore (~> 11.13.0)
  - firebase_analytics (11.5.0):
    - Firebase/Analytics (= 11.13.0)
    - firebase_core
    - Flutter
  - firebase_auth (5.6.0):
    - Firebase/Auth (= 11.13.0)
    - firebase_core
    - Flutter
  - firebase_core (3.14.0):
    - Firebase/CoreOnly (= 11.13.0)
    - Flutter
  - firebase_database (11.3.7):
    - Firebase/Database (= 11.13.0)
    - firebase_core
    - Flutter
  - FirebaseAnalytics (11.13.0):
    - FirebaseAnalytics/AdIdSupport (= 11.13.0)
    - FirebaseCore (~> 11.13.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - FirebaseAnalytics/AdIdSupport (11.13.0):
    - FirebaseCore (~> 11.13.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleAppMeasurement (= 11.13.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - FirebaseAppCheckInterop (11.15.0)
  - FirebaseAuth (11.13.0):
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseAuthInterop (~> 11.0)
    - FirebaseCore (~> 11.13.0)
    - FirebaseCoreExtension (~> 11.13.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/Environment (~> 8.1)
    - GTMSessionFetcher/Core (< 5.0, >= 3.4)
    - RecaptchaInterop (~> 101.0)
  - FirebaseAuthInterop (11.15.0)
  - FirebaseCore (11.13.0):
    - FirebaseCoreInternal (~> 11.13.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/Logger (~> 8.1)
  - FirebaseCoreExtension (11.13.0):
    - FirebaseCore (~> 11.13.0)
  - FirebaseCoreInternal (11.13.0):
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
  - FirebaseDatabase (11.13.0):
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseCore (~> 11.13.0)
    - FirebaseSharedSwift (~> 11.0)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - leveldb-library (~> 1.22)
  - FirebaseFirestore (11.13.0):
    - FirebaseFirestoreBinary (= 11.13.0)
  - FirebaseFirestoreAbseilBinary (1.2024072200.0)
  - FirebaseFirestoreBinary (11.13.0):
    - FirebaseCore (= 11.13.0)
    - FirebaseCoreExtension (= 11.13.0)
    - FirebaseFirestoreInternalBinary (= 11.13.0)
    - FirebaseSharedSwift (= 11.13.0)
  - FirebaseFirestoreGRPCBoringSSLBinary (1.69.0)
  - FirebaseFirestoreGRPCCoreBinary (1.69.0):
    - FirebaseFirestoreAbseilBinary (= 1.2024072200.0)
    - FirebaseFirestoreGRPCBoringSSLBinary (= 1.69.0)
  - FirebaseFirestoreGRPCCPPBinary (1.69.0):
    - FirebaseFirestoreAbseilBinary (= 1.2024072200.0)
    - FirebaseFirestoreGRPCCoreBinary (= 1.69.0)
  - FirebaseFirestoreInternalBinary (11.13.0):
    - FirebaseCore (= 11.13.0)
    - FirebaseFirestoreAbseilBinary (= 1.2024072200.0)
    - FirebaseFirestoreGRPCCPPBinary (= 1.69.0)
    - leveldb-library (~> 1.22)
    - nanopb (~> 3.30910.0)
  - FirebaseInstallations (11.13.0):
    - FirebaseCore (~> 11.13.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - PromisesObjC (~> 2.4)
  - FirebaseSharedSwift (11.13.0)
  - Flutter (1.0.0)
  - Google-Mobile-Ads-SDK (12.2.0):
    - GoogleUserMessagingPlatform (>= 1.1)
  - google_mobile_ads (6.0.0):
    - Flutter
    - Google-Mobile-Ads-SDK (~> 12.2.0)
    - webview_flutter_wkwebview
  - GoogleAppMeasurement (11.13.0):
    - GoogleAppMeasurement/AdIdSupport (= 11.13.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/AdIdSupport (11.13.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 11.13.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (11.13.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - GoogleUserMessagingPlatform (3.0.0)
  - GoogleUtilities/AppDelegateSwizzler (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.1.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.1.0)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/Reachability (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMSessionFetcher/Core (4.5.0)
  - leveldb-library (1.22.6)
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - PromisesObjC (2.4.0)
  - RecaptchaInterop (101.0.0)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - cloud_firestore (from `.symlinks/plugins/cloud_firestore/ios`)
  - firebase_analytics (from `.symlinks/plugins/firebase_analytics/ios`)
  - firebase_auth (from `.symlinks/plugins/firebase_auth/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_database (from `.symlinks/plugins/firebase_database/ios`)
  - FirebaseFirestore (from `https://github.com/invertase/firestore-ios-sdk-frameworks.git`, tag `11.13.0`)
  - Flutter (from `Flutter`)
  - google_mobile_ads (from `.symlinks/plugins/google_mobile_ads/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/darwin`)

SPEC REPOS:
  trunk:
    - Firebase
    - FirebaseAnalytics
    - FirebaseAppCheckInterop
    - FirebaseAuth
    - FirebaseAuthInterop
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseDatabase
    - FirebaseFirestoreAbseilBinary
    - FirebaseFirestoreBinary
    - FirebaseFirestoreGRPCBoringSSLBinary
    - FirebaseFirestoreGRPCCoreBinary
    - FirebaseFirestoreGRPCCPPBinary
    - FirebaseFirestoreInternalBinary
    - FirebaseInstallations
    - FirebaseSharedSwift
    - Google-Mobile-Ads-SDK
    - GoogleAppMeasurement
    - GoogleUserMessagingPlatform
    - GoogleUtilities
    - GTMSessionFetcher
    - leveldb-library
    - nanopb
    - PromisesObjC
    - RecaptchaInterop

EXTERNAL SOURCES:
  cloud_firestore:
    :path: ".symlinks/plugins/cloud_firestore/ios"
  firebase_analytics:
    :path: ".symlinks/plugins/firebase_analytics/ios"
  firebase_auth:
    :path: ".symlinks/plugins/firebase_auth/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_database:
    :path: ".symlinks/plugins/firebase_database/ios"
  FirebaseFirestore:
    :git: https://github.com/invertase/firestore-ios-sdk-frameworks.git
    :tag: 11.13.0
  Flutter:
    :path: Flutter
  google_mobile_ads:
    :path: ".symlinks/plugins/google_mobile_ads/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/darwin"

CHECKOUT OPTIONS:
  FirebaseFirestore:
    :git: https://github.com/invertase/firestore-ios-sdk-frameworks.git
    :tag: 11.13.0

SPEC CHECKSUMS:
  cloud_firestore: 2fdc3301bcc486957ff7fa705430a185468c1987
  Firebase: 3435bc66b4d494c2f22c79fd3aae4c1db6662327
  firebase_analytics: 4af7b20cf0c2da4e0473117e01a69507db8d7c16
  firebase_auth: ebc1f561cc3077efebad5005974c9ed58008973b
  firebase_core: 700bac7ed92bb754fd70fbf01d72b36ecdd6d450
  firebase_database: c808dcb003584f9925917e3b84c2a6547de31117
  FirebaseAnalytics: 630349facf4a114a0977e5d7570e104261973287
  FirebaseAppCheckInterop: 06fe5a3799278ae4667e6c432edd86b1030fa3df
  FirebaseAuth: 175cb5503dfdb52191b8ff81cdd52c1d9dee9ac9
  FirebaseAuthInterop: 7087d7a4ee4bc4de019b2d0c240974ed5d89e2fd
  FirebaseCore: c692c7f1c75305ab6aff2b367f25e11d73aa8bd0
  FirebaseCoreExtension: c048485c347616dba6165358dbef765c5197597b
  FirebaseCoreInternal: 29d7b3af4aaf0b8f3ed20b568c13df399b06f68c
  FirebaseDatabase: 56862f137a44061ca740bc05c57285fffb27becc
  FirebaseFirestore: e36466f4eaad740d03ceab30c221c958f16eba52
  FirebaseFirestoreAbseilBinary: 4cfa8823cedc1b774843e04fe578ad279b387f97
  FirebaseFirestoreBinary: 77bfe418e95c78d3c35e57053cc97c02f785d432
  FirebaseFirestoreGRPCBoringSSLBinary: c3dfef3ff448ae2c1c85f9baf9fac5afc4db99fa
  FirebaseFirestoreGRPCCoreBinary: 565534e160a0415d12185f7f171c52a567382fbd
  FirebaseFirestoreGRPCCPPBinary: 6c0134e8d230ee58b9d51dec2a30a48efd6d5dc7
  FirebaseFirestoreInternalBinary: cc52ee20219489d8f035f2ef0542953d0c79c66c
  FirebaseInstallations: 0ee9074f2c1e86561ace168ee1470dc67aabaf02
  FirebaseSharedSwift: aca73668bc95e8efccb618e0167eab05d19d3a75
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  Google-Mobile-Ads-SDK: 1dfb0c3cb46c7e2b00b0f4de74a1e06d9ea25d67
  google_mobile_ads: 535223588a6791b7a3cc3513a1bc7b89d12f3e62
  GoogleAppMeasurement: 0dfca1a4b534d123de3945e28f77869d10d0d600
  GoogleUserMessagingPlatform: f8d0cdad3ca835406755d0a69aa634f00e76d576
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  GTMSessionFetcher: fc75fc972958dceedee61cb662ae1da7a83a91cf
  leveldb-library: cc8b8f8e013647a295ad3f8cd2ddf49a6f19be19
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  RecaptchaInterop: 11e0b637842dfb48308d242afc3f448062325aba
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  webview_flutter_wkwebview: 1821ceac936eba6f7984d89a9f3bcb4dea99ebb2

PODFILE CHECKSUM: 04a393f167838127f9d45ae29dbc22ba4b174945

COCOAPODS: 1.16.2
