// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Turkish (`tr`).
class AppLocalizationsTr extends AppLocalizations {
  AppLocalizationsTr([String locale = 'tr']) : super(locale);

  @override
  String get appTitle => 'SpyOut';

  @override
  String get createRoom => 'Oda Oluştur';

  @override
  String get joinRoom => 'Odaya Katıl';

  @override
  String get roomCode => 'Oda Kodu';

  @override
  String get playerName => 'Oyuncu Adı';

  @override
  String get startGame => 'Oyunu Başlat';

  @override
  String get waitingForPlayers => 'Oyuncular bekleniyor...';

  @override
  String get yourRole => 'Rolünüz';

  @override
  String get civilian => 'Sivil';

  @override
  String get spy => 'Casus';

  @override
  String get location => 'Konum';

  @override
  String get timeRemaining => 'Kalan Süre';

  @override
  String get accusePlayer => 'Oyuncuyu Suçla';

  @override
  String get guessLocation => 'Konumu Tahmin Et';

  @override
  String get vote => 'Oy Ver';

  @override
  String get yes => 'Evet';

  @override
  String get no => 'Hayır';

  @override
  String get gameOver => 'Oyun Bitti';

  @override
  String get winner => 'Kazanan';

  @override
  String get score => 'Puan';

  @override
  String get nextRound => 'Sonraki Tur';

  @override
  String get newGame => 'Yeni Oyun';

  @override
  String get settings => 'Ayarlar';

  @override
  String get language => 'Dil';

  @override
  String get english => 'İngilizce';

  @override
  String get turkish => 'Türkçe';

  @override
  String get players => 'Oyuncular';

  @override
  String get round => 'Tur';

  @override
  String get accusation => 'Suçlama';

  @override
  String get voting => 'Oylama';

  @override
  String get spyWins => 'Casus Kazandı!';

  @override
  String get civiliansWin => 'Siviller Kazandı!';

  @override
  String get correctGuess => 'Doğru Tahmin!';

  @override
  String get wrongGuess => 'Yanlış Tahmin!';

  @override
  String get unanimousVote => 'Oybirliği Gerekli';

  @override
  String get accusationFailed => 'Suçlama Başarısız';

  @override
  String get timeUp => 'Süre Doldu!';

  @override
  String get askQuestion => 'Soru Sor';

  @override
  String get answer => 'Cevap';

  @override
  String get question => 'Soru';

  @override
  String get chat => 'Sohbet';

  @override
  String get send => 'Gönder';

  @override
  String get viewSpyLocations => 'Casusun Konumlarını Gör';

  @override
  String get spyLocationsList => 'Casusun Gördüğü Konumlar';

  @override
  String get noLocationsYet => 'Henüz hiç konum görülmedi';

  @override
  String get ultimateSpyGame => 'En İyi Casus Oyunu';

  @override
  String get hostInfo =>
      'Bu odanın ev sahibi olacaksınız. Diğer oyuncuların katılması için oda kodunu paylaşın.';

  @override
  String get joinRoomInfo =>
      'Oda kodu için ev sahibine sorun. Oda kodları 6 karakter uzunluğundadır.';

  @override
  String connectionError(String error) {
    return 'Bağlantı hatası: $error';
  }

  @override
  String get goBack => 'Geri Dön';

  @override
  String roomNotFound(String roomId) {
    return 'Oda bulunamadı\\nOda ID: $roomId';
  }

  @override
  String get roomCodeCopied => 'Oda kodu panoya kopyalandı';

  @override
  String get host => 'Ev Sahibi';

  @override
  String get needMinPlayers => 'Başlamak için en az 4 oyuncu gerekli';

  @override
  String failedToCreateRoom(String error) {
    return 'Oda oluşturulamadı: $error';
  }

  @override
  String failedToJoinRoom(String error) {
    return 'Odaya katılınamadı: $error';
  }

  @override
  String failedToStartGame(String error) {
    return 'Oyun başlatılamadı: $error';
  }

  @override
  String get accusesText => ' suçluyor: ';

  @override
  String get ofBeingSpyText => ' casus olmakla!';

  @override
  String get timeRemainingLabel => 'Kalan Süre';

  @override
  String get enterNameAndCode =>
      'Mevcut bir oyuna katılmak için adınızı ve oda kodunu girin';

  @override
  String get gameAlreadyStarted => 'Oyun zaten başlamış';

  @override
  String get roomIsFull => 'Oda dolu';

  @override
  String get roomNotFoundError => 'Oda bulunamadı';

  @override
  String get viewAllLocations => 'Tüm Konumları Gör';

  @override
  String get accused => 'SUÇLANAN';

  @override
  String get pleaseEnterName => 'Lütfen adınızı girin';

  @override
  String get enterNameToCreate =>
      'Yeni bir oyun odası oluşturmak için adınızı girin';

  @override
  String get whereDoYouThink => 'Nerede olduğunuzu düşünüyorsunuz?';

  @override
  String get cancel => 'İptal';

  @override
  String get changeLanguage => 'Dil Değiştir';

  @override
  String get allLocations => 'Tüm Konumlar';

  @override
  String get close => 'Kapat';

  @override
  String get leaveGame => 'Oyundan Çık?';

  @override
  String get leaveGameConfirm => 'Oyundan çıkmak istediğinizden emin misiniz?';

  @override
  String get leave => 'Çık';

  @override
  String get languageChanged => 'Dil başarıyla değiştirildi';

  @override
  String get selectPlayerToAccuse => 'Casusun kim olduğunu düşünüyorsunuz?';

  @override
  String get youAreAccused => 'Siz suçlanıyorsunuz ve oy veremezsiniz';

  @override
  String failedToAccusePlayer(String error) {
    return 'Oyuncu suçlanamadı: $error';
  }

  @override
  String get howToPlay => 'Nasıl Oynanır';

  @override
  String get howToPlayTitle => 'SpyOut Nasıl Oynanır?';

  @override
  String get gameObjective => 'Oyunun Amacı';

  @override
  String get gameObjectiveText =>
      'SpyOut, gizli kimlik ve dedüksiyon oyunudur. Oyuncular iki gruba ayrılır: Siviller ve Casus.';

  @override
  String get civilianObjective => 'Siviller İçin Amaç';

  @override
  String get civilianObjectiveText =>
      '• Casusu bulmak ve suçlamak\n• Sorular sorarak casusu ortaya çıkarmak\n• Diğer sivillerle işbirliği yapmak';

  @override
  String get spyObjective => 'Casus İçin Amaç';

  @override
  String get spyObjectiveText =>
      '• Kimliğini gizli tutmak\n• Konumu tahmin etmeye çalışmak\n• Siviller gibi davranmak';

  @override
  String get gameSetup => 'Oyun Kurulumu';

  @override
  String get gameSetupText =>
      '• Minimum 4, maksimum 10 oyuncu\n• Bir oyuncu oda oluşturur ve kodu paylaşır\n• Diğer oyuncular oda kodunu kullanarak katılır\n• Ev sahibi oyunu başlatır';

  @override
  String get gameFlow => 'Oyun Akışı';

  @override
  String get gameFlowText =>
      '• Oyun başladığında roller gizlice dağıtılır\n• Siviller konumu öğrenir, casus öğrenmez\n• Oyuncular sırayla soru sorar ve cevaplar\n• Herhangi bir oyuncu istediği zaman suçlama yapabilir';

  @override
  String get questionPhase => 'Soru Sorma Aşaması';

  @override
  String get questionPhaseText =>
      '• Her oyuncu diğer oyunculara sorular sorabilir\n• Sorular konumla ilgili olmalı ama çok açık olmamalı\n• Casus, konumu bilmediği için dikkatli cevap vermeli\n• Siviller, casusu yakalamaya çalışmalı';

  @override
  String get accusationPhase => 'Suçlama Aşaması';

  @override
  String get accusationPhaseText =>
      '• Herhangi bir oyuncu istediği zaman suçlama yapabilir\n• Suçlanan oyuncu oy veremez\n• Diğer tüm oyuncular oy kullanır\n• Oybirliği ile karar verilir';

  @override
  String get winConditions => 'Kazanma Koşulları';

  @override
  String get winConditionsText =>
      '• Siviller kazanır: Casusu doğru suçlarlarsa\n• Casus kazanır: Konumu doğru tahmin ederse\n• Casus kazanır: Yanlış suçlanırsa\n• Casus kazanır: Süre biterse';

  @override
  String get tips => 'İpuçları';

  @override
  String get tipsText =>
      '• Siviller: Açık sorular sormayın, casusu ele vermeyin\n• Siviller: Diğer oyuncuların cevaplarını dikkatle dinleyin\n• Casus: Konumu öğrenmek için akıllı sorular sorun\n• Casus: Şüphe çekmemek için doğal davranın\n• Herkes: Zaman sınırını unutmayın!';

  @override
  String get getHint => 'İpucu Al';

  @override
  String get watchAd => 'Reklam İzle';

  @override
  String get extraHint => 'Ekstra İpucu';

  @override
  String get locationNotInGame => 'Bu konum oyunda DEĞİL';

  @override
  String get noMoreHints => 'Başka ipucu yok';

  @override
  String get adNotReady =>
      'Reklam hazır değil, lütfen daha sonra tekrar deneyin';

  @override
  String get adFailedToShow => 'Reklam gösterilemedi';

  @override
  String get ok => 'Tamam';
}
