{"appTitle": "SpyOut", "createRoom": "Oda <PERSON>", "joinRoom": "Odaya Katıl", "roomCode": "<PERSON><PERSON>", "playerName": "Oyuncu Adı", "startGame": "<PERSON><PERSON><PERSON>", "waitingForPlayers": "Oyuncular bekleniyor...", "yourRole": "Rolünüz", "civilian": "Sivil", "spy": "Casus", "location": "<PERSON><PERSON>", "timeRemaining": "<PERSON><PERSON>", "accusePlayer": "Oyuncuyu Suçla", "guessLocation": "<PERSON><PERSON><PERSON>", "vote": "<PERSON><PERSON>", "yes": "<PERSON><PERSON>", "no": "Hay<PERSON><PERSON>", "gameOver": "Oyun Bitti", "winner": "<PERSON><PERSON>", "score": "<PERSON><PERSON>", "nextRound": "<PERSON><PERSON><PERSON>", "newGame": "<PERSON><PERSON>", "settings": "<PERSON><PERSON><PERSON>", "language": "Dil", "english": "İngilizce", "turkish": "Türkçe", "players": "Oyuncular", "round": "<PERSON><PERSON>", "accusation": "Suçlama", "voting": "<PERSON><PERSON><PERSON>", "spyWins": "Casus Kazandı!", "civiliansWin": "Siviller Kazandı!", "correctGuess": "<PERSON><PERSON><PERSON>!", "wrongGuess": "Yanlış Tahmin!", "unanimousVote": "Oybirliği Gerekli", "accusationFailed": "Suçlama Başarısız", "timeUp": "<PERSON><PERSON><PERSON>!", "askQuestion": "<PERSON><PERSON>", "answer": "<PERSON>va<PERSON>", "question": "<PERSON><PERSON>", "chat": "<PERSON><PERSON><PERSON>", "send": "<PERSON><PERSON><PERSON>", "viewSpyLocations": "Casusun Konumlarını Gör", "spyLocationsList": "Casusun Gördüğü Konumlar", "noLocationsYet": "<PERSON><PERSON><PERSON><PERSON> hiç konum görülmedi", "ultimateSpyGame": "En İyi Casus Oyunu", "hostInfo": "Bu odanın ev sahibi olacaksınız. Diğer oyuncuların katılması için oda kodunu paylaşın.", "joinRoomInfo": "<PERSON>da kodu i<PERSON>in ev sahibine sorun. Oda kodları 6 karakter uzunluğundadır.", "connectionError": "Bağlantı hatası: {error}", "@connectionError": {"placeholders": {"error": {"type": "String"}}}, "goBack": "<PERSON><PERSON>", "roomNotFound": "Oda bulunamadı\\nOda ID: {roomId}", "@roomNotFound": {"placeholders": {"roomId": {"type": "String"}}}, "roomCodeCopied": "Oda kodu panoya kopyalandı", "host": "<PERSON><PERSON>", "needMinPlayers": "Başlamak için en az 4 oyuncu gerekli", "failedToCreateRoom": "<PERSON><PERSON> oluşturulamadı: {error}", "@failedToCreateRoom": {"placeholders": {"error": {"type": "String"}}}, "failedToJoinRoom": "O<PERSON>a katılınamadı: {error}", "@failedToJoinRoom": {"placeholders": {"error": {"type": "String"}}}, "failedToStartGame": "<PERSON><PERSON> ba<PERSON>latılamadı: {error}", "@failedToStartGame": {"placeholders": {"error": {"type": "String"}}}, "accusesText": " suçluyor: ", "ofBeingSpyText": " casus olmakla!", "timeRemainingLabel": "<PERSON><PERSON>", "enterNameAndCode": "Mevcut bir oyuna katılmak için adınızı ve oda kodunu girin", "gameAlreadyStarted": "<PERSON>yun zaten başlamış", "roomIsFull": "<PERSON><PERSON> dolu", "roomNotFoundError": "<PERSON><PERSON> bulunamadı", "viewAllLocations": "<PERSON><PERSON>m Konumları Gör", "accused": "SUÇLANAN", "pleaseEnterName": "Lütfen adınızı girin", "enterNameToCreate": "Yeni bir oyun odası oluşturmak için adınızı girin", "whereDoYouThink": "Nerede olduğunuzu düşünüyorsunuz?", "cancel": "İptal", "changeLanguage": "<PERSON><PERSON>", "allLocations": "<PERSON><PERSON><PERSON>", "close": "Ka<PERSON><PERSON>", "leaveGame": "Oyundan Çık?", "leaveGameConfirm": "Oyundan çıkmak istediğinizden emin misiniz?", "leave": "Çık", "languageChanged": "<PERSON><PERSON> başarı<PERSON>", "selectPlayerToAccuse": "C<PERSON>un kim old<PERSON>u dü<PERSON>ünüyorsunuz?", "youAreAccused": "Siz suçlanıyorsunuz ve oy veremezsiniz", "failedToAccusePlayer": "Oyuncu suçlanamadı: {error}", "@failedToAccusePlayer": {"placeholders": {"error": {"type": "String"}}}, "howToPlay": "<PERSON><PERSON><PERSON><PERSON>", "howToPlayTitle": "SpyOut Nasıl Oynanır?", "gameObjective": "<PERSON><PERSON><PERSON>", "gameObjectiveText": "Spy<PERSON>ut, gizli kimlik ve dedüksiyon oyunudur. Oyuncular iki gruba ayrılır: Siviller ve Casus.", "civilianObjective": "Siviller <PERSON>", "civilianObjectiveText": "• <PERSON><PERSON>u bulmak ve suçlamak\n• Sorular sorarak casusu ortaya çıkarmak\n• Diğer sivillerle işbirliği yapmak", "spyObjective": "Casus İçin <PERSON>", "spyObjectiveText": "• Kimliğini gizli tutmak\n• <PERSON><PERSON><PERSON> tahmin etmeye çalışmak\n• Siviller gibi davranmak", "gameSetup": "<PERSON><PERSON>", "gameSetupText": "• Minimum 4, maks<PERSON><PERSON> 10 oyuncu\n• Bir oyuncu oda oluşturur ve kodu paylaşır\n• Diğer oyuncular oda kodunu kullanarak katılır\n• Ev sahibi oyunu başlatır", "gameFlow": "Oyun Akışı", "gameFlowText": "• Oyun başladığında roller gizlice dağıtılır\n• Siviller konumu <PERSON>, casus <PERSON>ğ<PERSON>\n• Oyuncular sırayla soru sorar ve cevaplar\n• Herhangi bir oyuncu istediği zaman suçlama yapabilir", "questionPhase": "<PERSON><PERSON>ı", "questionPhaseText": "• Her oyuncu diğer oyunculara sorular sorabilir\n• Sorular konumla ilgili olmalı ama çok açık olmamalı\n• Casus, konumu bilmediği için dikkatli cevap vermeli\n• Siviller, casusu yakalamaya çalışmalı", "accusationPhase": "Suçlama Aşaması", "accusationPhaseText": "• Herhangi bir oyuncu istediği zaman suçlama yapabilir\n• Suçlanan oyuncu oy veremez\n• <PERSON><PERSON>er tüm oyuncular oy kullanır\n• Oybirliği ile karar verilir", "winConditions": "Kazanma Koşulları", "winConditionsText": "• Siviller kazanır: <PERSON>asusu doğru suçlarlarsa\n• Casus kazanır: <PERSON><PERSON><PERSON> doğru tahmin ederse\n• Casus kazanır: Yanlış suçlanırsa\n• Casus kazanır: <PERSON><PERSON><PERSON> biterse", "tips": "İpuçları", "tipsText": "• Siviller: <PERSON><PERSON><PERSON><PERSON> sorular sorma<PERSON>ın, ca<PERSON><PERSON> ele vermey<PERSON>\n• Siviller: Diğer oyuncuların cevaplarını dikkatle dinleyin\n• Casus: Konumu öğrenmek için akıllı sorular sorun\n• Casus: <PERSON><PERSON><PERSON>mek için doğal davranın\n• Herkes: <PERSON><PERSON> sınırını unutmayın!", "getHint": "İpucu Al", "watchAd": "<PERSON><PERSON><PERSON>", "extraHint": "Ekstra İpucu", "locationNotInGame": "<PERSON>u konum oyunda DEĞİL", "noMoreHints": "Başka ipucu yok", "adNotReady": "<PERSON><PERSON><PERSON>, l<PERSON><PERSON><PERSON> daha sonra tekrar deneyin", "adFailedToShow": "<PERSON><PERSON><PERSON> gö<PERSON>", "ok": "<PERSON><PERSON>"}