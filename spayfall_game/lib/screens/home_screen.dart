import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../l10n/app_localizations.dart';
import '../services/locale_provider.dart';
import '../widgets/banner_ad_widget.dart';
import 'create_room_screen.dart';
import 'join_room_screen.dart';
import 'how_to_play_screen.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF0F0F23),
              Color(0xFF1A1A2E),
              Color(0xFF16213E),
            ],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Logo and Title
                Container(
                  padding: const EdgeInsets.all(32),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: const Color(0xFF0E4B99).withOpacity(0.2),
                    border: Border.all(
                      color: const Color(0xFF0E4B99),
                      width: 2,
                    ),
                  ),
                  child: const Icon(
                    Icons.visibility_off,
                    size: 64,
                    color: Color(0xFF0E4B99),
                  ),
                ),
                const SizedBox(height: 32),
                
                Text(
                  l10n.appTitle,
                  style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                    fontSize: 48,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                
                const SizedBox(height: 16),
                
                Text(
                  l10n.ultimateSpyGame,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Colors.white60,
                    fontSize: 18,
                  ),
                  textAlign: TextAlign.center,
                ),
                
                const SizedBox(height: 64),
                
                // Action Buttons
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const CreateRoomScreen(),
                        ),
                      );
                    },
                    icon: const Icon(Icons.add_circle_outline),
                    label: Text(
                      l10n.createRoom,
                      style: const TextStyle(fontSize: 18),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF0E4B99),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 20),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                    ),
                  ),
                ),
                
                const SizedBox(height: 16),
                
                SizedBox(
                  width: double.infinity,
                  child: OutlinedButton.icon(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const JoinRoomScreen(),
                        ),
                      );
                    },
                    icon: const Icon(Icons.login),
                    label: Text(
                      l10n.joinRoom,
                      style: const TextStyle(fontSize: 18),
                    ),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.white,
                      side: const BorderSide(color: Color(0xFF0E4B99), width: 2),
                      padding: const EdgeInsets.symmetric(vertical: 20),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                    ),
                  ),
                ),
                
                const SizedBox(height: 32),

                // How to Play Button
                TextButton.icon(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const HowToPlayScreen(),
                      ),
                    );
                  },
                  icon: const Icon(Icons.help_outline, color: Colors.white60),
                  label: Text(
                    l10n.howToPlay,
                    style: const TextStyle(color: Colors.white60),
                  ),
                ),

                const SizedBox(height: 16),

                // Settings Button
                TextButton.icon(
                  onPressed: () {
                    _showSettingsDialog(context);
                  },
                  icon: const Icon(Icons.settings, color: Colors.white60),
                  label: Text(
                    l10n.settings,
                    style: const TextStyle(color: Colors.white60),
                  ),
                ),

                const SizedBox(height: 24),

                // Banner Ad
                const BannerAdWidget(
                  margin: EdgeInsets.symmetric(horizontal: 16),
                ),

              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showSettingsDialog(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF16213E),
        title: Text(
          l10n.settings,
          style: const TextStyle(color: Colors.white),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.language, color: Colors.white60),
              title: Text(
                l10n.language,
                style: const TextStyle(color: Colors.white),
              ),
              subtitle: Text(
                localeProvider.locale.languageCode == 'tr' ? 'Türkçe' : 'English',
                style: const TextStyle(color: Colors.white60),
              ),
              onTap: () {
                Navigator.pop(context);
                _showLanguageDialog(context);
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(l10n.cancel),
          ),
        ],
      ),
    );
  }

  void _showLanguageDialog(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF16213E),
        title: Text(
          l10n.changeLanguage,
          style: const TextStyle(color: Colors.white),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.language, color: Colors.white60),
              title: const Text(
                'English',
                style: TextStyle(color: Colors.white),
              ),
              trailing: localeProvider.locale.languageCode == 'en'
                  ? const Icon(Icons.check, color: Color(0xFF0E4B99))
                  : null,
              onTap: () async {
                Navigator.pop(context);
                await localeProvider.setLanguage('en');
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(l10n.languageChanged ?? 'Language changed to English'),
                      backgroundColor: const Color(0xFF0E4B99),
                    ),
                  );
                }
              },
            ),
            ListTile(
              leading: const Icon(Icons.language, color: Colors.white60),
              title: const Text(
                'Türkçe',
                style: TextStyle(color: Colors.white),
              ),
              trailing: localeProvider.locale.languageCode == 'tr'
                  ? const Icon(Icons.check, color: Color(0xFF0E4B99))
                  : null,
              onTap: () async {
                Navigator.pop(context);
                await localeProvider.setLanguage('tr');
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Dil Türkçe olarak değiştirildi'),
                      backgroundColor: Color(0xFF0E4B99),
                    ),
                  );
                }
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(l10n.cancel),
          ),
        ],
      ),
    );
  }
}
