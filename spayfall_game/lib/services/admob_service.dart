import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';

class AdMobService {
  static final AdMobService _instance = AdMobService._internal();
  factory AdMobService() => _instance;
  AdMobService._internal();

  // Test Ad Unit IDs (replace with your actual Ad Unit IDs)
  static const String _androidBannerAdUnitId = 'ca-app-pub-3940256099942544/6300978111';
  static const String _iosBannerAdUnitId = 'ca-app-pub-3940256099942544/2934735716';
  static const String _androidInterstitialAdUnitId = 'ca-app-pub-3940256099942544/1033173712';
  static const String _iosInterstitialAdUnitId = 'ca-app-pub-3940256099942544/4411468910';
  static const String _androidRewardedAdUnitId = 'ca-app-pub-3940256099942544/5224354917';
  static const String _iosRewardedAdUnitId = 'ca-app-pub-3940256099942544/1712485313';

  // Production Ad Unit IDs (replace with your actual production Ad Unit IDs)
  static const String _androidBannerAdUnitIdProd = 'YOUR_ANDROID_BANNER_AD_UNIT_ID';
  static const String _iosBannerAdUnitIdProd = 'YOUR_IOS_BANNER_AD_UNIT_ID';
  static const String _androidInterstitialAdUnitIdProd = 'YOUR_ANDROID_INTERSTITIAL_AD_UNIT_ID';
  static const String _iosInterstitialAdUnitIdProd = 'YOUR_IOS_INTERSTITIAL_AD_UNIT_ID';
  static const String _androidRewardedAdUnitIdProd = 'YOUR_ANDROID_REWARDED_AD_UNIT_ID';
  static const String _iosRewardedAdUnitIdProd = 'YOUR_IOS_REWARDED_AD_UNIT_ID';

  // Current ad instances
  BannerAd? _bannerAd;
  InterstitialAd? _interstitialAd;
  RewardedAd? _rewardedAd;

  // Ad loading states
  bool _isBannerAdLoaded = false;
  bool _isInterstitialAdLoaded = false;
  bool _isRewardedAdLoaded = false;

  // Ad frequency control
  DateTime? _lastInterstitialShown;
  static const Duration _interstitialCooldown = Duration(minutes: 2);

  // Initialize AdMob
  Future<void> initialize() async {
    // Skip initialization on web platform
    if (kIsWeb) {
      if (kDebugMode) {
        print('🎯 AdMob skipped on web platform');
      }
      return;
    }

    await MobileAds.instance.initialize();

    // Set request configuration for better ad targeting
    final RequestConfiguration requestConfiguration = RequestConfiguration(
      testDeviceIds: kDebugMode ? ['YOUR_TEST_DEVICE_ID'] : [],
    );
    MobileAds.instance.updateRequestConfiguration(requestConfiguration);

    if (kDebugMode) {
      print('🎯 AdMob initialized successfully');
    }
  }

  // Get appropriate Ad Unit IDs based on platform and build mode
  String get bannerAdUnitId {
    if (kDebugMode) {
      return Platform.isAndroid ? _androidBannerAdUnitId : _iosBannerAdUnitId;
    } else {
      return Platform.isAndroid ? _androidBannerAdUnitIdProd : _iosBannerAdUnitIdProd;
    }
  }

  String get interstitialAdUnitId {
    if (kDebugMode) {
      return Platform.isAndroid ? _androidInterstitialAdUnitId : _iosInterstitialAdUnitId;
    } else {
      return Platform.isAndroid ? _androidInterstitialAdUnitIdProd : _iosInterstitialAdUnitIdProd;
    }
  }

  String get rewardedAdUnitId {
    if (kDebugMode) {
      return Platform.isAndroid ? _androidRewardedAdUnitId : _iosRewardedAdUnitId;
    } else {
      return Platform.isAndroid ? _androidRewardedAdUnitIdProd : _iosRewardedAdUnitIdProd;
    }
  }

  // Banner Ad Methods
  Future<void> loadBannerAd() async {
    if (kIsWeb) return;
    _bannerAd = BannerAd(
      adUnitId: bannerAdUnitId,
      size: AdSize.banner,
      request: const AdRequest(),
      listener: BannerAdListener(
        onAdLoaded: (ad) {
          _isBannerAdLoaded = true;
          if (kDebugMode) print('🎯 Banner ad loaded');
        },
        onAdFailedToLoad: (ad, error) {
          _isBannerAdLoaded = false;
          ad.dispose();
          if (kDebugMode) print('🎯 Banner ad failed to load: $error');
        },
        onAdOpened: (ad) {
          if (kDebugMode) print('🎯 Banner ad opened');
        },
        onAdClosed: (ad) {
          if (kDebugMode) print('🎯 Banner ad closed');
        },
      ),
    );
    
    await _bannerAd!.load();
  }

  BannerAd? get bannerAd => _isBannerAdLoaded ? _bannerAd : null;

  void disposeBannerAd() {
    _bannerAd?.dispose();
    _bannerAd = null;
    _isBannerAdLoaded = false;
  }

  // Interstitial Ad Methods
  Future<void> loadInterstitialAd() async {
    if (kIsWeb) return;
    await InterstitialAd.load(
      adUnitId: interstitialAdUnitId,
      request: const AdRequest(),
      adLoadCallback: InterstitialAdLoadCallback(
        onAdLoaded: (ad) {
          _interstitialAd = ad;
          _isInterstitialAdLoaded = true;
          if (kDebugMode) print('🎯 Interstitial ad loaded');
          
          _interstitialAd!.setImmersiveMode(true);
        },
        onAdFailedToLoad: (error) {
          _isInterstitialAdLoaded = false;
          if (kDebugMode) print('🎯 Interstitial ad failed to load: $error');
        },
      ),
    );
  }

  Future<bool> showInterstitialAd() async {
    if (kIsWeb) return false;
    if (!_isInterstitialAdLoaded || _interstitialAd == null) {
      if (kDebugMode) print('🎯 Interstitial ad not ready');
      return false;
    }

    // Check cooldown period
    if (_lastInterstitialShown != null) {
      final timeSinceLastShown = DateTime.now().difference(_lastInterstitialShown!);
      if (timeSinceLastShown < _interstitialCooldown) {
        if (kDebugMode) print('🎯 Interstitial ad cooldown active');
        return false;
      }
    }

    _interstitialAd!.fullScreenContentCallback = FullScreenContentCallback(
      onAdShowedFullScreenContent: (ad) {
        if (kDebugMode) print('🎯 Interstitial ad showed full screen');
      },
      onAdDismissedFullScreenContent: (ad) {
        if (kDebugMode) print('🎯 Interstitial ad dismissed');
        ad.dispose();
        _isInterstitialAdLoaded = false;
        _interstitialAd = null;
        _lastInterstitialShown = DateTime.now();
        
        // Preload next interstitial ad
        loadInterstitialAd();
      },
      onAdFailedToShowFullScreenContent: (ad, error) {
        if (kDebugMode) print('🎯 Interstitial ad failed to show: $error');
        ad.dispose();
        _isInterstitialAdLoaded = false;
        _interstitialAd = null;
      },
    );

    await _interstitialAd!.show();
    return true;
  }

  // Rewarded Ad Methods
  Future<void> loadRewardedAd() async {
    if (kIsWeb) return;
    await RewardedAd.load(
      adUnitId: rewardedAdUnitId,
      request: const AdRequest(),
      rewardedAdLoadCallback: RewardedAdLoadCallback(
        onAdLoaded: (ad) {
          _rewardedAd = ad;
          _isRewardedAdLoaded = true;
          if (kDebugMode) print('🎯 Rewarded ad loaded');
        },
        onAdFailedToLoad: (error) {
          _isRewardedAdLoaded = false;
          if (kDebugMode) print('🎯 Rewarded ad failed to load: $error');
        },
      ),
    );
  }

  Future<bool> showRewardedAd({
    required Function() onUserEarnedReward,
    Function()? onAdDismissed,
  }) async {
    if (kIsWeb) return false;
    if (!_isRewardedAdLoaded || _rewardedAd == null) {
      if (kDebugMode) print('🎯 Rewarded ad not ready');
      return false;
    }

    bool rewardEarned = false;

    _rewardedAd!.fullScreenContentCallback = FullScreenContentCallback(
      onAdShowedFullScreenContent: (ad) {
        if (kDebugMode) print('🎯 Rewarded ad showed full screen');
      },
      onAdDismissedFullScreenContent: (ad) {
        if (kDebugMode) print('🎯 Rewarded ad dismissed');
        ad.dispose();
        _isRewardedAdLoaded = false;
        _rewardedAd = null;
        
        if (onAdDismissed != null) {
          onAdDismissed();
        }
        
        // Preload next rewarded ad
        loadRewardedAd();
      },
      onAdFailedToShowFullScreenContent: (ad, error) {
        if (kDebugMode) print('🎯 Rewarded ad failed to show: $error');
        ad.dispose();
        _isRewardedAdLoaded = false;
        _rewardedAd = null;
      },
    );

    await _rewardedAd!.show(
      onUserEarnedReward: (ad, reward) {
        if (kDebugMode) print('🎯 User earned reward: ${reward.amount} ${reward.type}');
        rewardEarned = true;
        onUserEarnedReward();
      },
    );

    return rewardEarned;
  }

  // Utility methods
  bool get isBannerAdReady => _isBannerAdLoaded;
  bool get isInterstitialAdReady => _isInterstitialAdLoaded;
  bool get isRewardedAdReady => _isRewardedAdLoaded;

  // Preload all ads
  Future<void> preloadAds() async {
    await Future.wait([
      loadBannerAd(),
      loadInterstitialAd(),
      loadRewardedAd(),
    ]);
  }

  // Dispose all ads
  void dispose() {
    disposeBannerAd();
    _interstitialAd?.dispose();
    _rewardedAd?.dispose();
    _interstitialAd = null;
    _rewardedAd = null;
    _isInterstitialAdLoaded = false;
    _isRewardedAdLoaded = false;
  }
}
