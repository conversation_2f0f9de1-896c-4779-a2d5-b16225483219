import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import '../services/admob_service.dart';

class BannerAdWidget extends StatefulWidget {
  final EdgeInsets? margin;
  final bool autoLoad;

  const BannerAdWidget({
    super.key,
    this.margin,
    this.autoLoad = true,
  });

  @override
  State<BannerAdWidget> createState() => _BannerAdWidgetState();
}

class _BannerAdWidgetState extends State<BannerAdWidget> {
  AdMobService? _adMobService;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (widget.autoLoad) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _loadBannerAd();
      });
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _adMobService = Provider.of<AdMobService>(context, listen: false);
  }

  Future<void> _loadBannerAd() async {
    if (_adMobService == null || _isLoading) return;
    
    setState(() {
      _isLoading = true;
    });

    try {
      await _adMobService!.loadBannerAd();
    } catch (e) {
      debugPrint('Failed to load banner ad: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _adMobService?.disposeBannerAd();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Hide banner ads on web platform
    if (kIsWeb) {
      return const SizedBox.shrink();
    }

    if (_adMobService == null) {
      return const SizedBox.shrink();
    }

    final bannerAd = _adMobService!.bannerAd;

    if (_isLoading) {
      return Container(
        margin: widget.margin,
        height: 50,
        child: const Center(
          child: SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF0E4B99)),
            ),
          ),
        ),
      );
    }

    if (bannerAd == null || !_adMobService!.isBannerAdReady) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: widget.margin,
      alignment: Alignment.center,
      width: bannerAd.size.width.toDouble(),
      height: bannerAd.size.height.toDouble(),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: AdWidget(ad: bannerAd),
      ),
    );
  }
}
